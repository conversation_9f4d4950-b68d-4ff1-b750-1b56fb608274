import { NextResponse } from "next/server";
import type { KoboListResponse } from "@/types/kobo";
import { readFile } from "node:fs/promises";
import path from "node:path";

const BASE_URL = process.env.KFKOBO_BASE_URL ?? "https://kf.dharawalpindi.com";
const ASSET_ID = process.env.KFKOBO_ASSET_ID ?? "aFyQMvo4sF3PAUtBZ3iwXj";
const TOKEN = process.env.KFKOBO_TOKEN; // Do not default a token to avoid accidental leakage
const DJANGO_LANG = process.env.KFKOBO_DJANGO_LANG ?? "en";

export const dynamic = "force-dynamic";

export async function GET() {
  // Try remote first if token exists, else fallback to sample
  if (TOKEN) {
    try {
      const url = `${BASE_URL}/api/v2/assets/${ASSET_ID}/data.json`;
      const res = await fetch(url, {
        method: "GET",
        headers: {
          Authorization: TOKEN,
          Cookie: `django_language=${DJANGO_LANG}`,
        },
        // Do not cache sensitive data
        cache: "no-store",
      });

      if (!res.ok) {
        throw new Error(`Kobo API error: ${res.status} ${res.statusText}`);
      }

      const data = (await res.json()) as KoboListResponse;
      return NextResponse.json(data, { status: 200 });
    } catch (err) {
      // fall through to sample
      // eslint-disable-next-line no-console
      console.error(
        "Supervision API: remote fetch failed, using sample_response.json fallback:",
        err
      );
    }
  }

  // Fallback: read local sample file so UI can work offline
  try {
    const filePath = path.join(process.cwd(), "sample_response.json");
    const raw = await readFile(filePath, "utf-8");
    const json = JSON.parse(raw) as KoboListResponse;
    return NextResponse.json(json, { status: 200 });
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error("Supervision API: failed to read sample_response.json:", err);
    return NextResponse.json(
      { error: "Failed to load supervision data" },
      { status: 500 }
    );
  }
}
