import type { KoboListResponse, KoboResult } from "@/types/kobo";
import { findAttachmentFor, parseLocation } from "@/utils/kobo";
import Link from "next/link";

// Force dynamic to ensure fresh data
export const dynamic = "force-dynamic";

async function fetchAll(): Promise<KoboListResponse> {
  const url =
    "https://kf.dharawalpindi.com/api/v2/assets/aFyQMvo4sF3PAUtBZ3iwXj/data.json";
  const res = await fetch(url, {
    method: "GET",
    headers: {
      Authorization: "Token 1c77402fdb8443df0d3aa6a303a6ad3b6790a95a",
      Cookie: "django_language=en",
    },
    cache: "no-store",
  });
  if (!res.ok)
    throw new Error(`Failed to fetch supervision data: ${res.status}`);
  return res.json();
}

function Section({
  title,
  children,
}: {
  title: string;
  children: React.ReactNode;
}) {
  return (
    <section className="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">{title}</h2>
      {children}
    </section>
  );
}

// Client-side map component for submission markers (no SSR)
import SubmissionMap from "@/components/supervision/SubmissionMap";

function ItemRow({
  label,
  value,
}: {
  label: string;
  value?: string | number | null;
}) {
  return (
    <div className="grid grid-cols-3 gap-4 text-sm py-2 border-b border-gray-100 last:border-b-0">
      <div className="text-gray-600 font-medium">{label}</div>
      <div className="col-span-2 text-gray-900">{value ?? "-"}</div>
    </div>
  );
}

function HSBlock({ idx, s }: { idx: 1 | 2 | 3 | 4 | 5; s: KoboResult }) {
  const prefix = `group_hs_${idx}/hs_${idx}_` as const;

  // Helper to read a known KoboResult key without using any
  const getField = <K extends keyof KoboResult>(key: K): KoboResult[K] =>
    s[key];

  const loc = parseLocation(
    getField(`${prefix}location` as keyof KoboResult) as string | undefined
  );
  const q1 = getField(`${prefix}q1` as keyof KoboResult) as string | undefined;
  const q2 = getField(`${prefix}q2` as keyof KoboResult) as string | undefined;
  const q3 = getField(`${prefix}q3` as keyof KoboResult) as string | undefined;
  const q4 = getField(`${prefix}q4` as keyof KoboResult) as string | undefined;
  const q5 = getField(`${prefix}q5` as keyof KoboResult) as string | undefined;
  const q6 = getField(`${prefix}q6` as keyof KoboResult) as string | undefined;
  const q7 = getField(`${prefix}q7` as keyof KoboResult) as string | undefined;
  const q8 = getField(`${prefix}q8` as keyof KoboResult) as string | undefined;
  const q9 = getField(`${prefix}q9` as keyof KoboResult) as string | undefined;
  const q10 = getField(`${prefix}q10` as keyof KoboResult) as
    | string
    | undefined;

  const picXPath = `group_hs_${idx}/hs_${idx}_picture`;
  const attachment = findAttachmentFor(picXPath, s._attachments);

  return (
    <Section title={`Health Setting ${idx}`}>
      <div className="grid md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <div className="space-y-1">
            <ItemRow label="Code (q1)" value={q1} />
            <ItemRow label="Name (q2)" value={q2} />
            <ItemRow label="Phone (q3)" value={q3} />
            <ItemRow label="q4" value={q4} />
            <ItemRow label="q5 (date)" value={q5} />
            <ItemRow label="q6" value={q6} />
            <ItemRow label="q7" value={q7} />
            <ItemRow label="q8" value={q8} />
            <ItemRow label="q9" value={q9} />
            <ItemRow label="q10" value={q10} />
            <ItemRow
              label="Location"
              value={
                loc
                  ? `${loc.lat}, ${loc.lng}${
                      loc.alt ? `, alt ${loc.alt}` : ""
                    }${loc.acc ? `, acc ${loc.acc}` : ""}`
                  : undefined
              }
            />
          </div>
        </div>
        <div>
          {attachment ? (
            <div className="space-y-3">
              {/* Prefer small/medium thumbnails, fall back to original; add explicit size to avoid layout shift */}
              {/* Use Next.js Image for better loading and layout stability.
                  Unoptimized to proxy Kobo URLs as-is (since they are remote). */}
              <div className="relative w-full h-48 md:h-56">
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img
                  src={`/api/image-proxy?url=${encodeURIComponent(
                    attachment.download_url
                  )}`}
                  alt={`HS ${idx} picture`}
                  className="w-full h-full rounded-lg border object-cover bg-gray-50 shadow-sm"
                />
              </div>
              <div className="text-xs text-gray-600 break-all">
                <Link
                  className="text-blue-600 hover:text-blue-800 transition-colors"
                  href={attachment.download_url}
                  target="_blank"
                  rel="noreferrer"
                >
                  Open original image
                </Link>
              </div>
            </div>
          ) : (
            <div className="h-48 md:h-56 rounded-lg bg-gray-50 border-2 border-dashed border-gray-200 text-sm text-gray-400 flex items-center justify-center">
              <div className="text-center">
                <svg
                  className="w-8 h-8 mx-auto mb-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
                No picture available
              </div>
            </div>
          )}
        </div>
      </div>
    </Section>
  );
}

export default async function SupervisionDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  // In Next 15, route params are an async dynamic API and must be awaited.
  const { id } = await params;
  const numericId = Number(id);
  const data = await fetchAll();
  const submission = data.results.find((r) => r._id === numericId);

  if (!submission) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/" className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold">🦟</span>
                  </div>
                  <span className="text-xl font-semibold text-gray-900">
                    DTS
                  </span>
                </Link>
                <div className="h-6 w-px bg-gray-300"></div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Supervision Dashboard
                  </h1>
                  <p className="text-sm text-gray-600">
                    District Health Authority Rawalpindi
                  </p>
                </div>
              </div>
              <nav className="hidden md:flex space-x-6">
                <Link
                  href="/"
                  className="text-gray-600 hover:text-blue-600 transition-colors"
                >
                  Dashboard
                </Link>
                <Link
                  href="/supervision-dashboard"
                  className="text-blue-600 font-medium"
                >
                  Supervision
                </Link>
              </nav>
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto px-4 py-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="text-red-400 mr-3">
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div>
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">
                  Submission not found
                </p>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold">🦟</span>
                </div>
                <span className="text-xl font-semibold text-gray-900">DTS</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Supervision Dashboard
                </h1>
                <p className="text-sm text-gray-600">
                  District Health Authority Rawalpindi
                </p>
              </div>
            </div>
            <nav className="hidden md:flex space-x-6">
              <Link
                href="/"
                className="text-gray-600 hover:text-blue-600 transition-colors"
              >
                Dashboard
              </Link>
              <Link
                href="/supervision-dashboard"
                className="text-blue-600 font-medium"
              >
                Supervision
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-6">
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold text-gray-900">
              Submission #{submission._id}
            </h1>
            <span className="text-xs text-gray-500">
              {submission._submission_time
                ? new Date(submission._submission_time).toLocaleString()
                : ""}
            </span>
          </div>
        </div>

        {/* Map with 5 markers from this submission */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Visit Locations Map
          </h2>
          {/* Contain the map to avoid overlapping adjacent sections */}
          <div className="relative">
            <div className="w-full rounded-lg border border-gray-200 overflow-hidden">
              {/* Fix the height here so the internal Leaflet canvas cannot auto-expand */}
              <div className="h-72">
                <SubmissionMap submission={submission} />
              </div>
            </div>
          </div>
        </div>

        <Section title="General Information">
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <ItemRow label="Town" value={submission["group_general/town"]} />
              <ItemRow label="UC" value={submission["group_general/uc"]} />
              <ItemRow
                label="Supervisor CNIC"
                value={submission["group_general/supervisor_cnic"]}
              />
              <ItemRow
                label="Date of Visit"
                value={submission["group_general/Date_of_Visit"]}
              />
              <ItemRow
                label="Time of Visit"
                value={submission["group_general/Time_of_Visit"]}
              />
            </div>
            <div>
              <ItemRow
                label="Team Type"
                value={submission["group_general_001/Team_Type"]}
              />
              <ItemRow
                label="Dengue Team Number"
                value={submission["group_general_001/Dengue_Team_Number"]}
              />
              <ItemRow
                label="Team Member 1"
                value={submission["group_general_001/Team_Member_1_Name"]}
              />
              <ItemRow
                label="Team Member 2"
                value={submission["group_general_001/Team_Member_2_Name"]}
              />
              <ItemRow
                label="Area Address"
                value={
                  submission["group_general_001/group_ln8pu96/area_address"]
                }
              />
            </div>
          </div>
        </Section>

        <HSBlock idx={1} s={submission} />
        <HSBlock idx={2} s={submission} />
        <HSBlock idx={3} s={submission} />
        <HSBlock idx={4} s={submission} />
        <HSBlock idx={5} s={submission} />

        <Section title="Attachments">
          <div className="space-y-2">
            {submission._attachments.map((a) => (
              <div key={a.id} className="text-xs text-gray-700 break-all">
                <div className="font-medium">{a.question_xpath}</div>
                <Link
                  className="text-blue-600 hover:underline"
                  href={a.download_url}
                  target="_blank"
                  rel="noreferrer"
                >
                  {a.filename}
                </Link>
              </div>
            ))}
          </div>
        </Section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8 mt-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">🦟</span>
              </div>
              <span className="text-xl font-semibold">
                District Health Authority Rawalpindi
              </span>
            </div>
            <p className="text-gray-400">
              Powered by Epidemics Prevention & Control Cell
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
