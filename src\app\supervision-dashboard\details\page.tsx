import Link from "next/link";
import type { KoboListResponse, KoboResult } from "@/types/kobo";
import { getSubmissionInsights } from "@/utils/kobo";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore next/headers returns ReadonlyHeaders in this runtime; allow .get usage
import { headers } from "next/headers";

export const dynamic = "force-dynamic";

async function fetchData(): Promise<KoboListResponse> {
  // Use absolute URL to avoid ERR_INVALID_URL when node fetch lacks base.
  // In Next 15, headers() is an async dynamic API that must be awaited.
  const h = await (headers() as unknown as Promise<Headers>);
  const proto = h.get("x-forwarded-proto") ?? "http";
  const host = h.get("host") ?? "localhost:3000";
  const basePath = process.env.NEXT_PUBLIC_BASE_PATH ?? "";
  const origin = `${proto}://${host}`;
  const res = await fetch(`${origin}${basePath}/api/supervision`, {
    cache: "no-store",
  });
  if (!res.ok) {
    throw new Error(`Failed to load supervision data: ${res.status}`);
  }
  return res.json();
}

function Card({ item }: { item: KoboResult }) {
  const insights = getSubmissionInsights(item);
  return (
    <Link
      href={`/supervision-dashboard/details/${item._id}`}
      className="block rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-md transition p-4 bg-white"
    >
      <div className="flex gap-3">
        {insights.thumb ? (
          // eslint-disable-next-line @next/next/no-img-element
          <img
            src={insights.thumb}
            alt="thumb"
            className="w-20 h-20 object-cover rounded-md border"
          />
        ) : (
          <div className="w-20 h-20 rounded-md bg-gray-100 border flex items-center justify-center text-xs text-gray-400">
            No Image
          </div>
        )}
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-800">
              Submission #{item._id}
            </h3>
            <span className="text-xs text-gray-500">
              {item._submission_time
                ? new Date(item._submission_time).toLocaleString()
                : ""}
            </span>
          </div>
          <div className="mt-1 text-sm text-gray-700">
            <div>
              {insights.town} • {insights.uc}
            </div>
            <div>Supervisor CNIC: {insights.supervisorCnic ?? "-"}</div>
            <div>
              Visit: {insights.visitDate}{" "}
              {insights.visitTime ? `• ${insights.visitTime}` : ""}
            </div>
            <div>
              Team: {insights.teamType} • Members: {insights.teamMember1}
              {insights.teamMember2 ? `, ${insights.teamMember2}` : ""}
            </div>
            <div>Area: {insights.areaAddress ?? "-"}</div>
          </div>
          <div className="mt-2 flex items-center gap-2 text-xs">
            <span className="inline-flex items-center px-2 py-0.5 rounded bg-blue-50 text-blue-700 border border-blue-200">
              HS Count: {insights.hsCount}
            </span>
            {insights.riskFlags.map((f, idx) => (
              <span
                key={idx}
                className="inline-flex items-center px-2 py-0.5 rounded bg-amber-50 text-amber-700 border border-amber-200"
              >
                {f}
              </span>
            ))}
          </div>
        </div>
      </div>
    </Link>
  );
}

export default async function SupervisionDetailsListPage() {
  const data = await fetchData();

  return (
    <main className="p-4 md:p-6">
      <div className="mb-4">
        <h1 className="text-xl font-bold text-gray-900">
          Supervision Dashboard — Details
        </h1>
        <p className="text-sm text-gray-600">
          Listing {data.count} submissions
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
        {data.results.map((r) => (
          <Card key={r._id} item={r} />
        ))}
      </div>
    </main>
  );
}
