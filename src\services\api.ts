import axios from "axios";
import {
  Town,
  UC,
  ContainerData,
  SurveillanceFilters,
  SurveillanceResponse,
} from "@/types/surveillance";

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api/v1";

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log(
      "API Request:",
      config.method?.toUpperCase(),
      config.url,
      config.params
    );
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error("API Error Details:", {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
      url: error.config?.url,
      params: error.config?.params,
    });
    return Promise.reject(error);
  }
);

export const surveillanceApi = {
  // Get all towns
  getTowns: async (): Promise<Town[]> => {
    try {
      const response = await api.get("/towns");
      return response.data;
    } catch (error: unknown) {
      const err = error as Error & {
        response?: { status?: number; data?: unknown };
      };
      console.error("Error fetching towns:", {
        message: err.message,
        status: err.response?.status,
        data: err.response?.data,
      });
      // Return empty array if API fails
      return [];
    }
  },

  // Get UCs for a specific town
  getUCs: async (townCode: number): Promise<UC[]> => {
    try {
      const response = await api.get(`/towns/${townCode}/ucs`);
      return response.data;
    } catch (error: unknown) {
      const err = error as Error & {
        response?: { status?: number; data?: unknown };
      };
      console.error("Error fetching UCs:", {
        message: err.message,
        status: err.response?.status,
        data: err.response?.data,
      });
      // Return empty array if API fails
      return [];
    }
  },

  // Get surveillance data with filters
  getSurveillanceData: async (
    filters: SurveillanceFilters
  ): Promise<SurveillanceResponse> => {
    try {
      // According to the API spec, all three parameters are required
      if (!filters.townCode || !filters.ucCode) {
        console.warn(
          "Town code and UC code are required for surveillance data"
        );
        return {
          combined_data: [],
          container_data: [],
          users: [],
          total_records: 0,
        };
      }

      const params = {
        date: filters.date,
        town_code: filters.townCode,
        uc_code: filters.ucCode,
      };

      console.log("Sending request with params:", params);

      const response = await api.get<SurveillanceResponse>(
        "/surveillance-data",
        { params }
      );
      console.log("API Response:", response.data);

      // Return the full response
      return response.data;
    } catch (error: unknown) {
      const err = error as Error & {
        response?: { status?: number; data?: unknown };
        config?: { params?: unknown };
      };
      console.error("Error fetching surveillance data:", {
        message: err.message,
        status: err.response?.status,
        data: err.response?.data,
        params: err.config?.params,
      });

      // Return empty response instead of throwing to prevent app crash
      if (err.response?.status === 422) {
        console.warn("API returned 422 - returning empty response");
        return {
          combined_data: [],
          container_data: [],
          users: [],
          total_records: 0,
        };
      }

      throw error;
    }
  },

  // Get container data for a specific activity
  getContainerData: async (activityId: string): Promise<ContainerData[]> => {
    try {
      const response = await api.get(
        `/surveillance-data/${activityId}/containers`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching container data:", error);
      throw error;
    }
  },
};

export default api;
