import type { Kobo<PERSON>ttachment, KoboResult } from "@/types/kobo";

export type ParsedLocation = {
  lat: number;
  lng: number;
  alt?: number;
  acc?: number;
};

/**
 * Parse a Kobo location string like "33.6352407 73.0899091 469.5 17.992"
 * Returns lat/lng and optionally altitude and accuracy if present.
 */
export function parseLocation(loc?: string | null): ParsedLocation | undefined {
  if (!loc) return undefined;
  const parts = loc
    .trim()
    .split(/\s+/)
    .map(Number)
    .filter((n) => !Number.isNaN(n));
  if (parts.length < 2) return undefined;
  const [lat, lng, alt, acc] = parts;
  const result: ParsedLocation = { lat, lng };
  if (typeof alt === "number") result.alt = alt;
  if (typeof acc === "number") result.acc = acc;
  return result;
}

/**
 * Find the attachment matching a specific question xpath (e.g., "group_hs_1/hs_1_picture")
 */
export function findAttachmentFor(
  questionXPath: string,
  attachments?: KoboAttachment[]
): KoboAttachment | undefined {
  if (!attachments) return undefined;
  return attachments.find((a) => a.question_xpath === questionXPath);
}

/**
 * Get a displayable thumbnail URL (small) from the first available attachment
 */
export function getFirstThumb(
  attachments?: KoboAttachment[]
): string | undefined {
  if (!attachments || attachments.length === 0) return undefined;
  return (
    attachments[0].download_small_url ??
    attachments[0].download_medium_url ??
    attachments[0].download_url
  );
}

/**
 * Extract quick insights for a submission to show on list cards.
 */
export function getSubmissionInsights(s: KoboResult) {
  const hsCount = [
    s["group_hs_1/hs_1_q1"],
    s["group_hs_2/hs_2_q1"],
    s["group_hs_3/hs_3_q1"],
    s["group_hs_4/hs_4_q1"],
    s["group_hs_5/hs_5_q1"],
  ].filter(Boolean).length;

  // Risk flags based on the actual questions
  const riskFlags: string[] = [];

  // Check for larvae found (q7 = "کیا  ٹیم کو لاروہ ملا؟")
  const larvaeFoundFields = [
    s["group_hs_1/hs_1_q7"],
    s["group_hs_2/hs_2_q7"],
    s["group_hs_3/hs_3_q7"],
    s["group_hs_4/hs_4_q7"],
    s["group_hs_5/hs_5_q7"],
  ];
  if (larvaeFoundFields.some((v) => (v ?? "").toLowerCase() === "yes")) {
    riskFlags.push("Larvae Found");
  }

  // Check for larvae remaining (q8 = "کیا ٹیم سے لاروہ رہ گیا؟")
  const larvaeRemainingFields = [
    s["group_hs_1/hs_1_q8"],
    s["group_hs_2/hs_2_q8"],
    s["group_hs_3/hs_3_q8"],
    s["group_hs_4/hs_4_q8"],
    s["group_hs_5/hs_5_q8"],
  ];
  if (larvaeRemainingFields.some((v) => (v ?? "").toLowerCase() === "yes")) {
    riskFlags.push("Larvae Remaining");
  }

  // Check for fake work (q10 = "کیا ٹیم کا کام اس گھر میں fake ہے؟")
  const fakeWorkFields = [
    s["group_hs_1/hs_1_q10"],
    s["group_hs_2/hs_2_q10"],
    s["group_hs_3/hs_3_q10"],
    s["group_hs_4/hs_4_q10"],
    s["group_hs_5/hs_5_q10"],
  ];
  if (fakeWorkFields.some((v) => (v ?? "").toLowerCase() === "yes")) {
    riskFlags.push("Fake Work Detected");
  }

  // Check for incomplete inspections (q4 = "کیا ٹیم نے اندر سے گھر کا معائنہ کیا" or q6 = "کیا ٹیم نے چھت کا معائنہ کیا؟")
  const incompleteInspectionFields = [
    s["group_hs_1/hs_1_q4"],
    s["group_hs_1/hs_1_q6"],
    s["group_hs_2/hs_2_q4"],
    s["group_hs_2/hs_2_q6"],
    s["group_hs_3/hs_3_q4"],
    s["group_hs_3/hs_3_q6"],
    s["group_hs_4/hs_4_q4"],
    s["group_hs_4/hs_4_q6"],
    s["group_hs_5/hs_5_q4"],
    s["group_hs_5/hs_5_q6"],
  ];
  if (
    incompleteInspectionFields.some((v) => (v ?? "").toLowerCase() === "no")
  ) {
    riskFlags.push("Incomplete Inspection");
  }

  // Check for missing awareness (q9 = "کیا ٹیم نے اس دورے میں گھر والوں کو ڈینگی کے بارے میں آگاہی دی ہے؟")
  const noAwarenessFields = [
    s["group_hs_1/hs_1_q9"],
    s["group_hs_2/hs_2_q9"],
    s["group_hs_3/hs_3_q9"],
    s["group_hs_4/hs_4_q9"],
    s["group_hs_5/hs_5_q9"],
  ];
  if (noAwarenessFields.some((v) => (v ?? "").toLowerCase() === "no")) {
    riskFlags.push("No Awareness Provided");
  }

  return {
    hsCount,
    riskFlags,
    town: s["group_general/town"],
    uc: s["group_general/uc"],
    supervisorCnic: s["group_general/supervisor_cnic"],
    visitDate: s["group_general/Date_of_Visit"],
    visitTime: s["group_general/Time_of_Visit"],
    teamType: s["group_general_001/Team_Type"],
    teamMember1: s["group_general_001/Team_Member_1_Name"],
    teamMember2: s["group_general_001/Team_Member_2_Name"],
    areaAddress: s["group_general_001/group_ln8pu96/area_address"],
    thumb: getFirstThumb(s._attachments),
  };
}
